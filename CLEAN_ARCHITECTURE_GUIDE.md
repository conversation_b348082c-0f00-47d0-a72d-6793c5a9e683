# Clean Architecture Guide for AspireHub

This document explains the clean architecture implementation in the AspireHub Flutter application.

## Architecture Overview

The project follows Clean Architecture principles with clear separation of concerns across three main layers:

### 1. Domain Layer (Business Logic)
- **Location**: `lib/features/[feature_name]/domain/`
- **Purpose**: Contains business logic, entities, and repository interfaces
- **Dependencies**: No dependencies on other layers
- **Components**:
  - **Entities**: Core business objects (e.g., `UserEntity`, `PersonalityQuestionEntity`)
  - **Repositories**: Abstract interfaces for data access
  - **Use Cases**: Business logic operations

### 2. Data Layer (Data Access)
- **Location**: `lib/features/[feature_name]/data/`
- **Purpose**: Handles data from various sources (API, local storage)
- **Dependencies**: Depends only on Domain layer
- **Components**:
  - **Models**: Data transfer objects that extend domain entities
  - **Data Sources**: Remote (API) and Local (cache/database) data sources
  - **Repository Implementations**: Concrete implementations of domain repositories

### 3. Presentation Layer (UI)
- **Location**: `lib/features/[feature_name]/presentation/`
- **Purpose**: UI components and state management
- **Dependencies**: Depends on Domain layer only
- **Components**:
  - **Pages**: Screen widgets
  - **Widgets**: Reusable UI components
  - **BLoC**: State management (Business Logic Components)

## Folder Structure

```
lib/
├── core/                           # Core functionality
│   ├── constants/                  # App constants
│   ├── errors/                     # Error handling
│   ├── network/                    # Network utilities
│   ├── usecases/                   # Base use case classes
│   └── utils/                      # Utility functions
├── features/                       # Feature modules
│   ├── authentication/
│   │   ├── data/
│   │   │   ├── datasources/        # Remote & local data sources
│   │   │   ├── models/             # Data models
│   │   │   └── repositories/       # Repository implementations
│   │   ├── domain/
│   │   │   ├── entities/           # Business entities
│   │   │   ├── repositories/       # Repository interfaces
│   │   │   └── usecases/           # Business use cases
│   │   └── presentation/
│   │       ├── bloc/               # State management
│   │       ├── pages/              # Screen widgets
│   │       └── widgets/            # Feature-specific widgets
│   ├── personality_test/           # Same structure as authentication
│   ├── chat/                       # Same structure as authentication
│   └── dashboard/                  # Same structure as authentication
└── shared/                         # Shared components
    ├── data/                       # Shared data utilities
    └── presentation/               # Shared UI components
        ├── themes/                 # App themes
        └── widgets/                # Reusable widgets
```

## Key Features Implemented

### 1. Personality Test Feature
- **Domain**: Entities for questions and results, repository interface, use cases
- **Data**: Models, remote/local data sources, repository implementation
- **Presentation**: To be implemented with BLoC pattern

### 2. Authentication Feature
- **Domain**: User entity, auth repository interface
- **Data**: To be implemented
- **Presentation**: To be implemented

### 3. Chat Feature
- **Domain**: Chat and message entities, repository interface
- **Data**: To be implemented
- **Presentation**: To be implemented

## Dependency Injection

The project uses `get_it` for dependency injection:
- **Service Locator**: `lib/core/utils/service_locator.dart`
- **Registration**: All dependencies are registered in the `init()` function
- **Usage**: Dependencies are injected using `sl<Type>()`

## Error Handling

- **Failures**: Domain layer failures in `lib/core/errors/failures.dart`
- **Exceptions**: Data layer exceptions in `lib/core/errors/exceptions.dart`
- **Either Type**: Uses `dartz` package for functional error handling

## State Management

- **Pattern**: BLoC (Business Logic Component) pattern
- **Library**: `flutter_bloc`
- **Location**: `lib/features/[feature]/presentation/bloc/`

## Testing Strategy

Each layer should be tested independently:
- **Unit Tests**: Domain use cases and entities
- **Integration Tests**: Data repositories and data sources
- **Widget Tests**: Presentation layer components

## Migration from Current Structure

The existing code in the following folders needs to be migrated:
- `lib/models/` → Move to appropriate feature data models
- `lib/services/` → Move to appropriate feature data sources
- `lib/screens/` → Move to appropriate feature presentation pages
- `lib/widgets/` → Move to shared/presentation/widgets or feature-specific widgets
- `lib/controllers/` → Replace with BLoC pattern

## Benefits of This Architecture

1. **Separation of Concerns**: Each layer has a single responsibility
2. **Testability**: Easy to unit test business logic
3. **Maintainability**: Changes in one layer don't affect others
4. **Scalability**: Easy to add new features
5. **Independence**: Domain layer is independent of frameworks
6. **Flexibility**: Easy to change data sources or UI frameworks

## Next Steps

1. Implement BLoC state management for each feature
2. Migrate existing screens to the new structure
3. Add comprehensive error handling
4. Implement proper dependency injection
5. Add unit and integration tests
6. Update main.dart to use the new architecture
