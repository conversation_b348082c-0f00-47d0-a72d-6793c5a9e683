/// Career tracks and courses based on personality types
class CareerTracks {
  
  /// All available career tracks with their courses
  static const Map<String, Map<String, dynamic>> tracksData = {
    'تكنولوجيا': {
      'name': 'تكنولوجيا المعلومات',
      'description': 'تطوير البرمجيات وتقنيات الحاسوب',
      'icon': 'computer',
      'color': 0xFF1E88E5,
      'courses': [
        {
          'name': 'برمجة Python',
          'duration': '40 ساعة',
          'level': 'مبتدئ',
          'description': 'تعلم أساسيات البرمجة بلغة Python',
          'image': 'python_course.png'
        },
        {
          'name': 'تطوير المواقع',
          'duration': '60 ساعة',
          'level': 'متوسط',
          'description': 'HTML, CSS, JavaScript وReact',
          'image': 'web_dev_course.png'
        },
        {
          'name': 'علوم البيانات',
          'duration': '50 ساعة',
          'level': 'متقدم',
          'description': 'تحليل البيانات والذكاء الاصطناعي',
          'image': 'data_science_course.png'
        }
      ],
      'personalities': ['INTJ', 'INTP', 'ENTJ', 'ENTP']
    },
    
    'تصميم': {
      'name': 'التصميم والإبداع',
      'description': 'التصميم الجرافيكي وتجربة المستخدم',
      'icon': 'design_services',
      'color': 0xFFE91E63,
      'courses': [
        {
          'name': 'Figma للمبتدئين',
          'duration': '4.5 ساعة',
          'level': 'مبتدئ',
          'description': 'تعلم أساسيات التصميم باستخدام Figma',
          'image': 'figma_course.png'
        },
        {
          'name': 'Adobe Photoshop',
          'duration': '35 ساعة',
          'level': 'متوسط',
          'description': 'تحرير الصور والتصميم الجرافيكي',
          'image': 'photoshop_course.png'
        },
        {
          'name': 'UI/UX Design',
          'duration': '45 ساعة',
          'level': 'متقدم',
          'description': 'تصميم واجهات المستخدم وتجربة المستخدم',
          'image': 'uiux_course.png'
        }
      ],
      'personalities': ['INFP', 'ISFP', 'ENFP', 'ESFP', 'INFJ']
    },
    
    'إدارة أعمال': {
      'name': 'إدارة الأعمال',
      'description': 'القيادة والإدارة وريادة الأعمال',
      'icon': 'business',
      'color': 0xFF4CAF50,
      'courses': [
        {
          'name': 'أساسيات الإدارة',
          'duration': '30 ساعة',
          'level': 'مبتدئ',
          'description': 'مبادئ الإدارة والقيادة',
          'image': 'management_course.png'
        },
        {
          'name': 'ريادة الأعمال',
          'duration': '40 ساعة',
          'level': 'متوسط',
          'description': 'بناء وإدارة الشركات الناشئة',
          'image': 'entrepreneurship_course.png'
        },
        {
          'name': 'إدارة المشاريع',
          'duration': '35 ساعة',
          'level': 'متقدم',
          'description': 'تخطيط وتنفيذ المشاريع بكفاءة',
          'image': 'project_management_course.png'
        }
      ],
      'personalities': ['ENTJ', 'ESTJ', 'ENFJ', 'ESFJ']
    },
    
    'تسويق': {
      'name': 'التسويق الرقمي',
      'description': 'التسويق عبر الإنترنت ووسائل التواصل',
      'icon': 'campaign',
      'color': 0xFFFF9800,
      'courses': [
        {
          'name': 'أساسيات التسويق',
          'duration': '25 ساعة',
          'level': 'مبتدئ',
          'description': 'مبادئ التسويق والعلامة التجارية',
          'image': 'marketing_basics_course.png'
        },
        {
          'name': 'التسويق الرقمي',
          'duration': '40 ساعة',
          'level': 'متوسط',
          'description': 'Google Ads, Facebook Ads, SEO',
          'image': 'digital_marketing_course.png'
        },
        {
          'name': 'تحليل البيانات التسويقية',
          'duration': '30 ساعة',
          'level': 'متقدم',
          'description': 'Google Analytics وتحليل الأداء',
          'image': 'marketing_analytics_course.png'
        }
      ],
      'personalities': ['ENFP', 'ESFP', 'ENTP', 'ESTP']
    },
    
    'علم النفس': {
      'name': 'علم النفس والاستشارة',
      'description': 'فهم السلوك البشري والاستشارة النفسية',
      'icon': 'psychology',
      'color': 0xFF9C27B0,
      'courses': [
        {
          'name': 'مقدمة في علم النفس',
          'duration': '35 ساعة',
          'level': 'مبتدئ',
          'description': 'أساسيات علم النفس والسلوك',
          'image': 'psychology_intro_course.png'
        },
        {
          'name': 'علم النفس التطبيقي',
          'duration': '45 ساعة',
          'level': 'متوسط',
          'description': 'تطبيق علم النفس في الحياة العملية',
          'image': 'applied_psychology_course.png'
        },
        {
          'name': 'الاستشارة النفسية',
          'duration': '50 ساعة',
          'level': 'متقدم',
          'description': 'تقنيات الاستشارة والعلاج النفسي',
          'image': 'counseling_course.png'
        }
      ],
      'personalities': ['INFJ', 'ISFJ', 'ENFJ', 'ESFJ', 'INFP']
    },
    
    'هندسة': {
      'name': 'الهندسة والتقنية',
      'description': 'الهندسة الميكانيكية والكهربائية',
      'icon': 'engineering',
      'color': 0xFF607D8B,
      'courses': [
        {
          'name': 'أساسيات الهندسة',
          'duration': '40 ساعة',
          'level': 'مبتدئ',
          'description': 'مبادئ الهندسة والرياضيات التطبيقية',
          'image': 'engineering_basics_course.png'
        },
        {
          'name': 'AutoCAD',
          'duration': '35 ساعة',
          'level': 'متوسط',
          'description': 'الرسم الهندسي والتصميم ثلاثي الأبعاد',
          'image': 'autocad_course.png'
        },
        {
          'name': 'إدارة المشاريع الهندسية',
          'duration': '30 ساعة',
          'level': 'متقدم',
          'description': 'تخطيط وتنفيذ المشاريع الهندسية',
          'image': 'engineering_pm_course.png'
        }
      ],
      'personalities': ['ISTJ', 'ISTP', 'INTJ', 'ENTJ']
    }
  };

  /// Get tracks for specific personality type
  static List<String> getTracksForPersonality(String personalityType) {
    List<String> matchingTracks = [];
    
    tracksData.forEach((trackName, trackData) {
      List<String> personalities = List<String>.from(trackData['personalities']);
      if (personalities.contains(personalityType)) {
        matchingTracks.add(trackName);
      }
    });
    
    return matchingTracks;
  }

  /// Get track data by name
  static Map<String, dynamic>? getTrackData(String trackName) {
    return tracksData[trackName];
  }

  /// Get all tracks
  static Map<String, Map<String, dynamic>> getAllTracks() {
    return tracksData;
  }

  /// Get courses for specific track
  static List<Map<String, dynamic>> getCoursesForTrack(String trackName) {
    final trackData = getTrackData(trackName);
    if (trackData != null) {
      return List<Map<String, dynamic>>.from(trackData['courses']);
    }
    return [];
  }

  /// Get recommended tracks with priority (primary, secondary)
  static Map<String, List<String>> getRecommendedTracks(String personalityType) {
    final allTracks = getTracksForPersonality(personalityType);
    
    // Define primary tracks for each personality
    final primaryTrackMap = {
      'INTJ': ['تكنولوجيا', 'هندسة'],
      'INTP': ['تكنولوجيا'],
      'ENTJ': ['إدارة أعمال', 'تكنولوجيا'],
      'ENTP': ['تسويق', 'تكنولوجيا'],
      'INFJ': ['علم النفس', 'تصميم'],
      'INFP': ['تصميم', 'علم النفس'],
      'ENFJ': ['إدارة أعمال', 'علم النفس'],
      'ENFP': ['تسويق', 'تصميم'],
      'ISTJ': ['هندسة', 'إدارة أعمال'],
      'ISFJ': ['علم النفس', 'إدارة أعمال'],
      'ESTJ': ['إدارة أعمال', 'هندسة'],
      'ESFJ': ['إدارة أعمال', 'علم النفس'],
      'ISTP': ['هندسة', 'تكنولوجيا'],
      'ISFP': ['تصميم'],
      'ESTP': ['تسويق', 'إدارة أعمال'],
      'ESFP': ['تسويق', 'تصميم'],
    };
    
    final primaryTracks = primaryTrackMap[personalityType] ?? [];
    final secondaryTracks = allTracks.where((track) => !primaryTracks.contains(track)).toList();
    
    return {
      'primary': primaryTracks,
      'secondary': secondaryTracks,
    };
  }
}
