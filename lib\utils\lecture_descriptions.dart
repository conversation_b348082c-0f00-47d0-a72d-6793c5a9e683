/// Detailed lecture descriptions for courses
class LectureDescriptions {
  
  /// Get detailed description for Figma lecture
  static String getFigmaLectureDescription(int lectureNumber) {
    switch (lectureNumber) {
      case 1:
        return "Welcome to the Figma course! In this introduction, you'll learn about Figma's interface, basic tools, and how to navigate the workspace. We'll cover the fundamentals that will set you up for success in your design journey.";
      case 2:
        return "Learn how to set up your first Figma project. We'll explore creating new files, understanding frames, and organizing your workspace for maximum productivity.";
      case 3:
        return "Master the fundamental building blocks of Figma design. Discover how to work with frames, create and manipulate shapes, and understand the relationship between different elements.";
      case 4:
        return "Dive deep into typography and text styling in Figma. Learn about font selection, text properties, character and paragraph spacing, and creating beautiful text layouts.";
      case 5:
        return "Explore the world of colors and gradients in Figma. Understand color theory, create color palettes, apply gradients, and manage color styles for consistent design.";
      case 6:
        return "Discover the power of components and variants in Figma. Learn how to create reusable design elements, manage component libraries, and build scalable design systems.";
      case 7:
        return "Master Figma's Auto Layout feature for responsive design. Learn how to create flexible layouts that adapt to different content and screen sizes automatically.";
      case 8:
        return "Bring your designs to life with prototyping and interactions. Learn how to create clickable prototypes, add animations, and simulate user experiences.";
      case 9:
        return "Learn best practices for team collaboration and developer handoff. Discover how to share designs, gather feedback, and prepare assets for development.";
      case 10:
        return "Explore advanced Figma techniques and professional workflows. Learn about plugins, advanced features, and tips that will elevate your design process.";
      case 11:
        return "Apply everything you've learned in a complete mobile app design project. From concept to final prototype, create a professional mobile application.";
      case 12:
        return "Complete your learning journey with a comprehensive web design project. Design a full website using all the Figma skills and best practices you've mastered.";
      default:
        return "Continue your Figma learning journey with this comprehensive lesson covering essential design concepts and practical techniques.";
    }
  }

  /// Get learning objectives for a lecture
  static List<String> getFigmaLearningObjectives(int lectureNumber) {
    switch (lectureNumber) {
      case 1:
        return [
          "Navigate Figma interface confidently",
          "Understand basic tools and panels",
          "Set up your workspace efficiently"
        ];
      case 2:
        return [
          "Create new Figma projects",
          "Organize files and pages",
          "Understand frame concepts"
        ];
      case 3:
        return [
          "Master shape tools",
          "Work with frames effectively",
          "Understand design hierarchy"
        ];
      case 4:
        return [
          "Apply typography principles",
          "Style text professionally",
          "Create text systems"
        ];
      case 5:
        return [
          "Apply color theory",
          "Create color palettes",
          "Use gradients effectively"
        ];
      case 6:
        return [
          "Build reusable components",
          "Create design systems",
          "Manage component libraries"
        ];
      case 7:
        return [
          "Master Auto Layout",
          "Create responsive designs",
          "Build flexible layouts"
        ];
      case 8:
        return [
          "Create interactive prototypes",
          "Add smooth animations",
          "Test user flows"
        ];
      case 9:
        return [
          "Collaborate with teams",
          "Prepare developer handoffs",
          "Share designs effectively"
        ];
      case 10:
        return [
          "Use advanced features",
          "Optimize workflows",
          "Leverage plugins"
        ];
      case 11:
        return [
          "Design complete mobile app",
          "Apply UX principles",
          "Create professional prototypes"
        ];
      case 12:
        return [
          "Design responsive websites",
          "Apply all learned skills",
          "Create portfolio projects"
        ];
      default:
        return ["Learn essential Figma skills"];
    }
  }
}
