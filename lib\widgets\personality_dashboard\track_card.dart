import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TrackCard extends StatelessWidget {
  final String trackName;
  final Map<String, dynamic> trackData;
  final bool isPrimary;
  final VoidCallback onTap;

  const TrackCard({
    super.key,
    required this.trackName,
    required this.trackData,
    required this.isPrimary,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final color = Color(trackData['color']);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: isPrimary ? 200.w : 160.w,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color.withOpacity(isPrimary ? 0.8 : 0.6),
              color.withOpacity(isPrimary ? 0.6 : 0.4),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8.r,
              offset: Offset(0, 3.h),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                if (isPrimary)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'مناسب لك',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const Spacer(),
                Icon(
                  _getIconData(trackData['icon']),
                  color: Colors.white,
                  size: isPrimary ? 24.sp : 20.sp,
                ),
              ],
            ),
            
            SizedBox(height: 8.h),
            
            Text(
              trackData['name'],
              style: TextStyle(
                color: Colors.white,
                fontSize: isPrimary ? 16.sp : 14.sp,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            if (isPrimary) ...[
              SizedBox(height: 4.h),
              Text(
                trackData['description'],
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 12.sp,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'computer':
        return Icons.computer;
      case 'design_services':
        return Icons.design_services;
      case 'business':
        return Icons.business;
      case 'campaign':
        return Icons.campaign;
      case 'psychology':
        return Icons.psychology;
      case 'engineering':
        return Icons.engineering;
      default:
        return Icons.school;
    }
  }
}
