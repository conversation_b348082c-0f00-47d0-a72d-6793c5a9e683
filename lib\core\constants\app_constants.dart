/// Application constants
class AppConstants {
  // API Constants
  static const String baseUrl = 'https://mocki.io/v1/';
  static const String personalityTestEndpoint = '16personalities-questions';
  
  // Storage Keys
  static const String userTypeKey = 'user_type';
  static const String firstLaunchKey = 'first_launch';
  static const String themeKey = 'theme_mode';
  static const String profileImagePathKey = 'profileImagePath';
  static const String userNameKey = 'user_name';
  static const String userEmailKey = 'user_email';
  
  // User Types
  static const String studentUserType = 'student';
  static const String companyUserType = 'company';
  
  // Theme Values
  static const String darkTheme = 'dark';
  static const String lightTheme = 'light';
  
  // Network
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Pagination
  static const int defaultPageSize = 20;
  
  // Animation Durations
  static const int defaultAnimationDuration = 300; // milliseconds
  static const int splashScreenDuration = 2000; // milliseconds
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  
  // File Paths
  static const String companyStartImagePath = 'assets/images/company_start.png';
  static const String speechBubbleImagePath = 'assets/images/app_images/speech-bubble.svg';
  static const String testImagePath = 'assets/images/app_images/test_image.png';
}
