import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../domain/entities/personality_question_entity.dart';
import '../../domain/entities/personality_result_entity.dart';
import '../../domain/repositories/personality_test_repository.dart';
import '../datasources/personality_test_local_data_source.dart';
import '../datasources/personality_test_remote_data_source.dart';
import '../models/personality_result_model.dart';

/// Implementation of personality test repository
class PersonalityTestRepositoryImpl implements PersonalityTestRepository {
  final PersonalityTestRemoteDataSource remoteDataSource;
  final PersonalityTestLocalDataSource localDataSource;

  PersonalityTestRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, List<PersonalityQuestionEntity>>> getQuestions() async {
    try {
      final questions = await remoteDataSource.getQuestions();
      return Right(questions);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, PersonalityResultEntity>> calculateResult(
    Map<int, int> answers,
  ) async {
    try {
      // Calculate personality type based on answers
      final result = _calculatePersonalityType(answers);
      return Right(result);
    } catch (e) {
      return Left(ServerFailure('Failed to calculate result: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> saveResult(PersonalityResultEntity result) async {
    try {
      final resultModel = PersonalityResultModel.fromEntity(result);
      await localDataSource.saveResult(resultModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Failed to save result: $e'));
    }
  }

  @override
  Future<Either<Failure, PersonalityResultEntity?>> getSavedResult() async {
    try {
      final result = await localDataSource.getSavedResult();
      return Right(result);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Failed to get saved result: $e'));
    }
  }

  PersonalityResultEntity _calculatePersonalityType(Map<int, int> answers) {
    // Simple calculation logic - in a real app this would be more sophisticated
    final scores = <String, int>{
      'E': 0, 'I': 0,
      'S': 0, 'N': 0,
      'T': 0, 'F': 0,
      'J': 0, 'P': 0,
    };

    // Calculate scores based on answers
    // This is a simplified version - real implementation would be more complex
    
    final personalityType = _determinePersonalityType(scores);
    
    return PersonalityResultEntity(
      personalityType: personalityType,
      scores: scores,
      description: _getDescription(personalityType),
      strengths: _getStrengths(personalityType),
      weaknesses: _getWeaknesses(personalityType),
      careerSuggestions: _getCareerSuggestions(personalityType),
    );
  }

  String _determinePersonalityType(Map<String, int> scores) {
    final type = StringBuffer();
    type.write(scores['E']! > scores['I']! ? 'E' : 'I');
    type.write(scores['S']! > scores['N']! ? 'S' : 'N');
    type.write(scores['T']! > scores['F']! ? 'T' : 'F');
    type.write(scores['J']! > scores['P']! ? 'J' : 'P');
    return type.toString();
  }

  String _getDescription(String type) {
    // Return description based on personality type
    return 'Description for $type personality type';
  }

  List<String> _getStrengths(String type) {
    // Return strengths based on personality type
    return ['Strength 1', 'Strength 2', 'Strength 3'];
  }

  List<String> _getWeaknesses(String type) {
    // Return weaknesses based on personality type
    return ['Weakness 1', 'Weakness 2'];
  }

  List<String> _getCareerSuggestions(String type) {
    // Return career suggestions based on personality type
    return ['Career 1', 'Career 2', 'Career 3'];
  }
}
