import 'package:shared_preferences/shared_preferences.dart';
import '../models/personality_types.dart';
import '../models/personality_questions.dart';
import '../models/career_tracks.dart';

/// Service for managing personality data and recommendations
class PersonalityService {
  static const String _personalityKey = 'user_personality_type';
  static const String _quizCompletedKey = 'quiz_completed';
  static const String _lastQuizDateKey = 'last_quiz_date';

  /// Save user's personality type
  static Future<void> savePersonalityType(String personalityType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_personalityKey, personalityType);
    await prefs.setBool(_quizCompletedKey, true);
    await prefs.setString(_lastQuizDateKey, DateTime.now().toIso8601String());
  }

  /// Get user's personality type
  static Future<String?> getPersonalityType() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_personalityKey);
  }

  /// Check if user has completed the quiz
  static Future<bool> hasCompletedQuiz() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_quizCompletedKey) ?? false;
  }

  /// Get last quiz completion date
  static Future<DateTime?> getLastQuizDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_lastQuizDateKey);
    return dateString != null ? DateTime.parse(dateString) : null;
  }

  /// Clear personality data (for retaking quiz)
  static Future<void> clearPersonalityData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_personalityKey);
    await prefs.remove(_quizCompletedKey);
    await prefs.remove(_lastQuizDateKey);
  }

  /// Generate random quiz questions
  static List<Map<String, dynamic>> generateQuizQuestions() {
    return PersonalityQuestions.generateRandomQuestions();
  }

  /// Calculate personality type from quiz answers
  static String calculatePersonalityType(List<Map<String, dynamic>> answers) {
    final scores = <String, int>{};
    
    for (var answer in answers) {
      final dimension = answer['dimension'] as String;
      final score = answer['score'] as int;
      scores[dimension] = (scores[dimension] ?? 0) + score;
    }
    
    return PersonalityQuestions.calculatePersonalityType(scores);
  }

  /// Get personality information
  static Map<String, dynamic>? getPersonalityInfo(String personalityType) {
    return PersonalityTypes.getPersonalityData(personalityType);
  }

  /// Get recommended tracks for personality
  static Map<String, List<String>> getRecommendedTracks(String personalityType) {
    return CareerTracks.getRecommendedTracks(personalityType);
  }

  /// Get courses for recommended tracks
  static List<Map<String, dynamic>> getRecommendedCourses(String personalityType) {
    final recommendedTracks = getRecommendedTracks(personalityType);
    final allRecommended = [...recommendedTracks['primary']!, ...recommendedTracks['secondary']!];
    
    final courses = <Map<String, dynamic>>[];
    
    for (String trackName in allRecommended) {
      final trackCourses = CareerTracks.getCoursesForTrack(trackName);
      for (var course in trackCourses) {
        courses.add({
          ...course,
          'track': trackName,
          'trackData': CareerTracks.getTrackData(trackName),
        });
      }
    }
    
    return courses;
  }

  /// Get dashboard configuration based on personality
  static Map<String, dynamic> getDashboardConfig(String personalityType) {
    final personalityInfo = getPersonalityInfo(personalityType);
    final recommendedTracks = getRecommendedTracks(personalityType);
    
    return {
      'personalityType': personalityType,
      'personalityInfo': personalityInfo,
      'primaryTracks': recommendedTracks['primary'],
      'secondaryTracks': recommendedTracks['secondary'],
      'recommendedCourses': getRecommendedCourses(personalityType),
      'personalityColor': PersonalityTypes.getColorForPersonality(personalityType),
    };
  }

  /// Check if user should retake quiz (after 6 months)
  static Future<bool> shouldRetakeQuiz() async {
    final lastQuizDate = await getLastQuizDate();
    if (lastQuizDate == null) return true;
    
    final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
    return lastQuizDate.isBefore(sixMonthsAgo);
  }

  /// Get personality statistics
  static Map<String, dynamic> getPersonalityStats(String personalityType) {
    final personalityInfo = getPersonalityInfo(personalityType);
    final allTypes = PersonalityTypes.getAllTypes();
    
    return {
      'type': personalityType,
      'name': personalityInfo?['name'] ?? 'غير محدد',
      'description': personalityInfo?['description'] ?? '',
      'traits': personalityInfo?['traits'] ?? [],
      'strengths': personalityInfo?['strengths'] ?? [],
      'careers': personalityInfo?['careers'] ?? [],
      'rarity': '${(100 / allTypes.length).toStringAsFixed(1)}%', // Simplified rarity
      'color': PersonalityTypes.getColorForPersonality(personalityType),
    };
  }

  /// Get learning style recommendations based on personality
  static Map<String, dynamic> getLearningStyleRecommendations(String personalityType) {
    // Extract dimensions from personality type
    final isExtrovert = personalityType.startsWith('E');
    final isSensing = personalityType.contains('S');
    final isThinking = personalityType.contains('T');
    final isJudging = personalityType.endsWith('J');
    
    return {
      'preferredLearningMethods': [
        if (isExtrovert) 'التعلم الجماعي والمناقشات',
        if (!isExtrovert) 'التعلم الفردي والقراءة',
        if (isSensing) 'التطبيق العملي والأمثلة الواقعية',
        if (!isSensing) 'النظريات والمفاهيم المجردة',
        if (isThinking) 'التحليل المنطقي والبيانات',
        if (!isThinking) 'القصص والتجارب الشخصية',
        if (isJudging) 'التعلم المنظم والمخطط',
        if (!isJudging) 'التعلم المرن والاستكشافي',
      ],
      'studyTips': [
        if (isExtrovert) 'انضم لمجموعات دراسية',
        if (!isExtrovert) 'خصص وقتاً للدراسة الفردية',
        if (isSensing) 'ركز على التطبيقات العملية',
        if (!isSensing) 'ابحث عن الروابط والأنماط',
        if (isThinking) 'استخدم الرسوم البيانية والإحصائيات',
        if (!isThinking) 'اربط المعلومات بالقيم الشخصية',
        if (isJudging) 'ضع جدولاً زمنياً للدراسة',
        if (!isJudging) 'نوع في طرق التعلم',
      ],
    };
  }
}
