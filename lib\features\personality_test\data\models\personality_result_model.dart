import '../../domain/entities/personality_result_entity.dart';

/// Data model for personality result
class PersonalityResultModel extends PersonalityResultEntity {
  const PersonalityResultModel({
    required super.personalityType,
    required super.scores,
    required super.description,
    required super.strengths,
    required super.weaknesses,
    required super.careerSuggestions,
  });

  factory PersonalityResultModel.fromJson(Map<String, dynamic> json) {
    return PersonalityResultModel(
      personalityType: json['personalityType'],
      scores: Map<String, int>.from(json['scores']),
      description: json['description'],
      strengths: List<String>.from(json['strengths']),
      weaknesses: List<String>.from(json['weaknesses']),
      careerSuggestions: List<String>.from(json['careerSuggestions']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'personalityType': personalityType,
      'scores': scores,
      'description': description,
      'strengths': strengths,
      'weaknesses': weaknesses,
      'careerSuggestions': careerSuggestions,
    };
  }

  factory PersonalityResultModel.fromEntity(PersonalityResultEntity entity) {
    return PersonalityResultModel(
      personalityType: entity.personalityType,
      scores: entity.scores,
      description: entity.description,
      strengths: entity.strengths,
      weaknesses: entity.weaknesses,
      careerSuggestions: entity.careerSuggestions,
    );
  }
}
