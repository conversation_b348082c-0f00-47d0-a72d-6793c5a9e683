import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/personality_result_entity.dart';
import '../repositories/personality_test_repository.dart';

/// Use case for calculating personality test result
class CalculatePersonalityResult extends UseCase<PersonalityResultEntity, CalculatePersonalityResultParams> {
  final PersonalityTestRepository repository;

  CalculatePersonalityResult(this.repository);

  @override
  Future<Either<Failure, PersonalityResultEntity>> call(CalculatePersonalityResultParams params) async {
    return await repository.calculateResult(params.answers);
  }
}

/// Parameters for calculating personality result
class CalculatePersonalityResultParams extends Equatable {
  final Map<int, int> answers;

  const CalculatePersonalityResultParams({required this.answers});

  @override
  List<Object> get props => [answers];
}
