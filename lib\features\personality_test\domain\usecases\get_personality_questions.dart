import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/personality_question_entity.dart';
import '../repositories/personality_test_repository.dart';

/// Use case for getting personality test questions
class GetPersonalityQuestions extends UseCaseNoParams<List<PersonalityQuestionEntity>> {
  final PersonalityTestRepository repository;

  GetPersonalityQuestions(this.repository);

  @override
  Future<Either<Failure, List<PersonalityQuestionEntity>>> call() async {
    return await repository.getQuestions();
  }
}
