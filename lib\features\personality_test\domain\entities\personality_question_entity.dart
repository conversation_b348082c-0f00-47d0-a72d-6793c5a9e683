import 'package:equatable/equatable.dart';

/// Domain entity for personality question
class PersonalityQuestionEntity extends Equatable {
  final int id;
  final String question;
  final List<PersonalityOptionEntity> options;
  final String? imagePath;
  final String category;

  const PersonalityQuestionEntity({
    required this.id,
    required this.question,
    required this.options,
    this.imagePath,
    required this.category,
  });

  @override
  List<Object?> get props => [id, question, options, imagePath, category];
}

/// Domain entity for personality option
class PersonalityOptionEntity extends Equatable {
  final String text;
  final String trait;
  final int value;

  const PersonalityOptionEntity({
    required this.text,
    required this.trait,
    required this.value,
  });

  @override
  List<Object> get props => [text, trait, value];
}
