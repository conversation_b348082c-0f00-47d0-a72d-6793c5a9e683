import 'package:equatable/equatable.dart';
import 'chat_entity.dart';

/// Domain entity for chat message
class ChatMessageEntity extends Equatable {
  final String id;
  final String senderId;
  final String receiverId;
  final String content;
  final DateTime timestamp;
  final MessageType type;
  final bool isRead;

  const ChatMessageEntity({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.timestamp,
    required this.type,
    required this.isRead,
  });

  @override
  List<Object> get props => [
        id,
        senderId,
        receiverId,
        content,
        timestamp,
        type,
        isRead,
      ];
}
