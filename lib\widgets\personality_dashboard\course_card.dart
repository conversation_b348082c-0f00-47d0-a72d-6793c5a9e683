import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CourseCard extends StatelessWidget {
  final Map<String, dynamic> course;
  final VoidCallback onTap;

  const CourseCard({
    super.key,
    required this.course,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final trackData = course['trackData'] as Map<String, dynamic>;
    final trackColor = Color(trackData['color']);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Theme.of(context).cardTheme.color,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: Row(
          children: [
            // Course Image/Icon
            Container(
              width: 60.w,
              height: 60.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    trackColor.withOpacity(0.8),
                    trackColor.withOpacity(0.6),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                _getIconForTrack(course['track']),
                color: Colors.white,
                size: 24.sp,
              ),
            ),
            
            SizedBox(width: 16.w),
            
            // Course Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    course['name'],
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleMedium?.color,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: 4.h),
                  
                  Text(
                    course['description'],
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: 8.h),
                  
                  Row(
                    children: [
                      // Track Badge
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: trackColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          course['track'],
                          style: TextStyle(
                            color: trackColor,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      
                      SizedBox(width: 8.w),
                      
                      // Duration
                      Icon(
                        Icons.access_time,
                        size: 12.sp,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        course['duration'],
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                        ),
                      ),
                      
                      SizedBox(width: 12.w),
                      
                      // Level
                      Icon(
                        Icons.signal_cellular_alt,
                        size: 12.sp,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        course['level'],
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Arrow Icon
            Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconForTrack(String trackName) {
    switch (trackName) {
      case 'تكنولوجيا':
        return Icons.computer;
      case 'تصميم':
        return Icons.design_services;
      case 'إدارة أعمال':
        return Icons.business;
      case 'تسويق':
        return Icons.campaign;
      case 'علم النفس':
        return Icons.psychology;
      case 'هندسة':
        return Icons.engineering;
      default:
        return Icons.school;
    }
  }
}
