import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

/// Service for handling YouTube video playback
class YouTubePlayerService {
  
  /// Open YouTube video in external app or browser
  static Future<void> openYouTubeVideo(String videoId) async {
    final youtubeUrl = 'https://www.youtube.com/watch?v=$videoId';
    final youtubeAppUrl = 'youtube://watch?v=$videoId';
    
    try {
      // Try to open in YouTube app first
      if (await canLaunchUrl(Uri.parse(youtubeAppUrl))) {
        await launchUrl(Uri.parse(youtubeAppUrl));
      } else {
        // Fallback to browser
        await launchUrl(
          Uri.parse(youtubeUrl),
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      debugPrint('Error opening YouTube video: $e');
      // Show error message to user
      throw Exception('Unable to open video. Please check your internet connection.');
    }
  }
  
  /// Open YouTube playlist in external app or browser
  static Future<void> openYouTubePlaylist(String playlistId) async {
    final playlistUrl = 'https://www.youtube.com/playlist?list=$playlistId';
    final youtubeAppUrl = 'youtube://playlist?list=$playlistId';
    
    try {
      // Try to open in YouTube app first
      if (await canLaunchUrl(Uri.parse(youtubeAppUrl))) {
        await launchUrl(Uri.parse(youtubeAppUrl));
      } else {
        // Fallback to browser
        await launchUrl(
          Uri.parse(playlistUrl),
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      debugPrint('Error opening YouTube playlist: $e');
      throw Exception('Unable to open playlist. Please check your internet connection.');
    }
  }
  
  /// Get YouTube thumbnail URL
  static String getThumbnailUrl(String videoId, {String quality = 'maxresdefault'}) {
    return 'https://img.youtube.com/vi/$videoId/$quality.jpg';
  }
  
  /// Get YouTube video embed URL
  static String getEmbedUrl(String videoId) {
    return 'https://www.youtube.com/embed/$videoId';
  }
  
  /// Extract video ID from YouTube URL
  static String? extractVideoId(String url) {
    final regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }
  
  /// Extract playlist ID from YouTube URL
  static String? extractPlaylistId(String url) {
    final regExp = RegExp(
      r'[?&]list=([^&]+)',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }
  
  /// Show video player dialog with options
  static Future<void> showVideoPlayerDialog(
    BuildContext context,
    String videoId,
    String title,
  ) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Video thumbnail
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  getThumbnailUrl(videoId),
                  height: 120,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(Icons.video_library, size: 50),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'اختر طريقة المشاهدة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton.icon(
              onPressed: () async {
                Navigator.of(context).pop();
                try {
                  await openYouTubeVideo(videoId);
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(e.toString())),
                    );
                  }
                }
              },
              icon: const Icon(Icons.play_arrow),
              label: const Text('مشاهدة'),
            ),
          ],
        );
      },
    );
  }
  
  /// Show playlist dialog with options
  static Future<void> showPlaylistDialog(
    BuildContext context,
    String playlistId,
    String title,
  ) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.playlist_play, size: 60, color: Colors.red),
              SizedBox(height: 16),
              Text(
                'فتح قائمة التشغيل في YouTube؟',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton.icon(
              onPressed: () async {
                Navigator.of(context).pop();
                try {
                  await openYouTubePlaylist(playlistId);
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(e.toString())),
                    );
                  }
                }
              },
              icon: const Icon(Icons.playlist_play),
              label: const Text('فتح القائمة'),
            ),
          ],
        );
      },
    );
  }
}
