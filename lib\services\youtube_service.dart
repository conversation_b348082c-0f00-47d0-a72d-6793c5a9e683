/// YouTube Service for handling video playback and playlist management
class YouTubeService {
  // Figma Course Playlist Information
  static const String playlistId = 'PLjzhiGLyugKwnM6uN4NXhfpU8L7XvtDEv';
  static const String playlistUrl = 'https://youtube.com/playlist?list=PLjzhiGLyugKwnM6uN4NXhfpU8L7XvtDEv&si=G7k_2PB99a5JItta';
  
  /// Generate YouTube video URL from video ID
  static String getVideoUrl(String videoId) {
    return 'https://www.youtube.com/watch?v=$videoId';
  }
  
  /// Generate YouTube thumbnail URL from video ID
  static String getThumbnailUrl(String videoId) {
    return 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg';
  }
  
  /// Generate YouTube embed URL from video ID
  static String getEmbedUrl(String videoId) {
    return 'https://www.youtube.com/embed/$videoId';
  }
  
  /// Open YouTube video in external app or browser
  static Future<void> openVideo(String videoId) async {
    final url = getVideoUrl(videoId);
    // In a real implementation, you would use url_launcher package
    // await launchUrl(Uri.parse(url));
    print('Opening YouTube video: $url');
  }
  
  /// Open the complete playlist in YouTube
  static Future<void> openPlaylist() async {
    // In a real implementation, you would use url_launcher package
    // await launchUrl(Uri.parse(playlistUrl));
    print('Opening YouTube playlist: $playlistUrl');
  }
}
