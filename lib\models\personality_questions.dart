import 'dart:math';

/// Personality Quiz Questions System
class PersonalityQuestions {
  
  /// All available questions for personality test
  static const List<Map<String, dynamic>> allQuestions = [
    // Extraversion vs Introversion (E/I)
    {
      'id': 'ei_1',
      'question': 'في الحفلات، أنت عادة:',
      'answers': [
        {'text': 'أتفاعل مع الكثير من الناس، بما في ذلك الغرباء', 'dimension': 'E', 'score': 2},
        {'text': 'أتفاعل مع القليل من الأشخاص المعروفين لي', 'dimension': 'I', 'score': 2},
      ]
    },
    {
      'id': 'ei_2',
      'question': 'أي من هذين يصفك أكثر؟',
      'answers': [
        {'text': 'أحب أن أكون محور الاهتمام', 'dimension': 'E', 'score': 2},
        {'text': 'أفضل البقاء في الخلفية', 'dimension': 'I', 'score': 2},
      ]
    },
    {
      'id': 'ei_3',
      'question': 'عندما تحتاج للتفكير في مشكلة معقدة:',
      'answers': [
        {'text': 'أناقشها مع الآخرين', 'dimension': 'E', 'score': 2},
        {'text': 'أفكر فيها بمفردي', 'dimension': 'I', 'score': 2},
      ]
    },
    {
      'id': 'ei_4',
      'question': 'في نهاية يوم طويل ومرهق:',
      'answers': [
        {'text': 'أحب الخروج مع الأصدقاء', 'dimension': 'E', 'score': 2},
        {'text': 'أفضل البقاء في المنزل والاسترخاء', 'dimension': 'I', 'score': 2},
      ]
    },
    
    // Sensing vs Intuition (S/N)
    {
      'id': 'sn_1',
      'question': 'عند تعلم شيء جديد، تفضل:',
      'answers': [
        {'text': 'التركيز على الحقائق والتفاصيل', 'dimension': 'S', 'score': 2},
        {'text': 'فهم الصورة الكبيرة والمفاهيم', 'dimension': 'N', 'score': 2},
      ]
    },
    {
      'id': 'sn_2',
      'question': 'أي من هذين يجذبك أكثر؟',
      'answers': [
        {'text': 'الأشياء العملية والواقعية', 'dimension': 'S', 'score': 2},
        {'text': 'الأفكار والإمكانيات المستقبلية', 'dimension': 'N', 'score': 2},
      ]
    },
    {
      'id': 'sn_3',
      'question': 'عند قراءة كتاب:',
      'answers': [
        {'text': 'أركز على الأحداث والتفاصيل', 'dimension': 'S', 'score': 2},
        {'text': 'أبحث عن المعاني والرموز الخفية', 'dimension': 'N', 'score': 2},
      ]
    },
    {
      'id': 'sn_4',
      'question': 'في العمل، تفضل:',
      'answers': [
        {'text': 'المهام الواضحة والمحددة', 'dimension': 'S', 'score': 2},
        {'text': 'المشاريع الإبداعية والمفتوحة', 'dimension': 'N', 'score': 2},
      ]
    },
    
    // Thinking vs Feeling (T/F)
    {
      'id': 'tf_1',
      'question': 'عند اتخاذ قرار مهم:',
      'answers': [
        {'text': 'أحلل الحقائق والمنطق', 'dimension': 'T', 'score': 2},
        {'text': 'أفكر في تأثيره على الناس', 'dimension': 'F', 'score': 2},
      ]
    },
    {
      'id': 'tf_2',
      'question': 'أي من هذين أهم في القيادة؟',
      'answers': [
        {'text': 'الكفاءة والعدالة', 'dimension': 'T', 'score': 2},
        {'text': 'التعاطف والانسجام', 'dimension': 'F', 'score': 2},
      ]
    },
    {
      'id': 'tf_3',
      'question': 'عندما ينتقدك شخص ما:',
      'answers': [
        {'text': 'أركز على صحة النقد', 'dimension': 'T', 'score': 2},
        {'text': 'أتأثر بمشاعري أولاً', 'dimension': 'F', 'score': 2},
      ]
    },
    {
      'id': 'tf_4',
      'question': 'في النزاعات، تميل إلى:',
      'answers': [
        {'text': 'التركيز على من محق ومن مخطئ', 'dimension': 'T', 'score': 2},
        {'text': 'البحث عن حل يرضي الجميع', 'dimension': 'F', 'score': 2},
      ]
    },
    
    // Judging vs Perceiving (J/P)
    {
      'id': 'jp_1',
      'question': 'تفضل أن تكون حياتك:',
      'answers': [
        {'text': 'منظمة ومخططة', 'dimension': 'J', 'score': 2},
        {'text': 'مرنة وعفوية', 'dimension': 'P', 'score': 2},
      ]
    },
    {
      'id': 'jp_2',
      'question': 'عند التخطيط لرحلة:',
      'answers': [
        {'text': 'أخطط لكل التفاصيل مسبقاً', 'dimension': 'J', 'score': 2},
        {'text': 'أترك مجالاً للمفاجآت', 'dimension': 'P', 'score': 2},
      ]
    },
    {
      'id': 'jp_3',
      'question': 'في المشاريع، تفضل:',
      'answers': [
        {'text': 'إنهاءها قبل الموعد النهائي', 'dimension': 'J', 'score': 2},
        {'text': 'العمل تحت ضغط الوقت', 'dimension': 'P', 'score': 2},
      ]
    },
    {
      'id': 'jp_4',
      'question': 'مكتبك أو غرفتك عادة:',
      'answers': [
        {'text': 'منظمة ومرتبة', 'dimension': 'J', 'score': 2},
        {'text': 'فوضوية ولكن أعرف مكان كل شيء', 'dimension': 'P', 'score': 2},
      ]
    },
    
    // Additional questions for more variety
    {
      'id': 'ei_5',
      'question': 'أفضل طريقة للتعلم بالنسبة لي:',
      'answers': [
        {'text': 'المناقشة الجماعية', 'dimension': 'E', 'score': 2},
        {'text': 'القراءة والدراسة الفردية', 'dimension': 'I', 'score': 2},
      ]
    },
    {
      'id': 'sn_5',
      'question': 'عند حل المشاكل:',
      'answers': [
        {'text': 'أستخدم الطرق المجربة', 'dimension': 'S', 'score': 2},
        {'text': 'أبحث عن طرق جديدة ومبتكرة', 'dimension': 'N', 'score': 2},
      ]
    },
    {
      'id': 'tf_5',
      'question': 'أقدر أكثر:',
      'answers': [
        {'text': 'الصدق المباشر', 'dimension': 'T', 'score': 2},
        {'text': 'اللطف والتكتم', 'dimension': 'F', 'score': 2},
      ]
    },
    {
      'id': 'jp_5',
      'question': 'في اتخاذ القرارات:',
      'answers': [
        {'text': 'أحب اتخاذها بسرعة', 'dimension': 'J', 'score': 2},
        {'text': 'أفضل إبقاء الخيارات مفتوحة', 'dimension': 'P', 'score': 2},
      ]
    },
  ];

  /// Generate random set of questions (16 questions, 4 from each dimension)
  static List<Map<String, dynamic>> generateRandomQuestions() {
    final random = Random();
    final selectedQuestions = <Map<String, dynamic>>[];
    
    // Get 4 questions from each dimension
    final dimensions = ['ei', 'sn', 'tf', 'jp'];
    
    for (String dimension in dimensions) {
      final dimensionQuestions = allQuestions
          .where((q) => q['id'].toString().startsWith(dimension))
          .toList();
      
      // Shuffle and take 4 questions from this dimension
      dimensionQuestions.shuffle(random);
      selectedQuestions.addAll(dimensionQuestions.take(4));
    }
    
    // Shuffle the final list
    selectedQuestions.shuffle(random);
    
    return selectedQuestions;
  }

  /// Calculate personality type from answers
  static String calculatePersonalityType(Map<String, int> scores) {
    String result = '';
    
    // E vs I
    final eScore = scores['E'] ?? 0;
    final iScore = scores['I'] ?? 0;
    result += eScore > iScore ? 'E' : 'I';
    
    // S vs N
    final sScore = scores['S'] ?? 0;
    final nScore = scores['N'] ?? 0;
    result += sScore > nScore ? 'S' : 'N';
    
    // T vs F
    final tScore = scores['T'] ?? 0;
    final fScore = scores['F'] ?? 0;
    result += tScore > fScore ? 'T' : 'F';
    
    // J vs P
    final jScore = scores['J'] ?? 0;
    final pScore = scores['P'] ?? 0;
    result += jScore > pScore ? 'J' : 'P';
    
    return result;
  }
}
