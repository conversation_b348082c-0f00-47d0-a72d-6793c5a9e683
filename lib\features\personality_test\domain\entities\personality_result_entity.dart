import 'package:equatable/equatable.dart';

/// Domain entity for personality test result
class PersonalityResultEntity extends Equatable {
  final String personalityType;
  final Map<String, int> scores;
  final String description;
  final List<String> strengths;
  final List<String> weaknesses;
  final List<String> careerSuggestions;

  const PersonalityResultEntity({
    required this.personalityType,
    required this.scores,
    required this.description,
    required this.strengths,
    required this.weaknesses,
    required this.careerSuggestions,
  });

  @override
  List<Object> get props => [
        personalityType,
        scores,
        description,
        strengths,
        weaknesses,
        careerSuggestions,
      ];
}
