import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/personality_question_entity.dart';
import '../entities/personality_result_entity.dart';

/// Repository interface for personality test
abstract class PersonalityTestRepository {
  /// Get personality test questions
  Future<Either<Failure, List<PersonalityQuestionEntity>>> getQuestions();
  
  /// Calculate personality result based on answers
  Future<Either<Failure, PersonalityResultEntity>> calculateResult(
    Map<int, int> answers,
  );
  
  /// Save personality result
  Future<Either<Failure, void>> saveResult(PersonalityResultEntity result);
  
  /// Get saved personality result
  Future<Either<Failure, PersonalityResultEntity?>> getSavedResult();
}
