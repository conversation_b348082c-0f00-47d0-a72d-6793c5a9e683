/// Real YouTube courses with actual video IDs
class YouTubeCourses {
  
  /// Figma Course with Real YouTube Videos
  static const String figmaPlaylistId = 'PLjzhiGLyugKwnM6uN4NXhfpU8L7XvtDEv';
  static const String figmaPlaylistUrl = 'https://youtube.com/playlist?list=PLjzhiGLyugKwnM6uN4NXhfpU8L7XvtDEv&si=G7k_2PB99a5JItta';
  
  static final List<Map<String, dynamic>> figmaLectures = [
    {
      'lec': 1,
      'title': 'Figma Tutorial for Beginners - Introduction',
      'time': 15,
      'description': 'Learn the basics of Figma interface and tools',
      'videoId': 'FTFaQWZBqQ8',
      'youtubeUrl': 'https://www.youtube.com/watch?v=FTFaQWZBqQ8',
    },
    {
      'lec': 2,
      'title': 'Getting Started with Figma',
      'time': 12,
      'description': 'Create your first Figma project and understand the workspace',
      'videoId': 'Cx2dkpBxst8',
      'youtubeUrl': 'https://www.youtube.com/watch?v=Cx2dkpBxst8',
    },
    {
      'lec': 3,
      'title': 'Figma Basics - Frames and Shapes',
      'time': 18,
      'description': 'Master the fundamental building blocks of Figma design',
      'videoId': 'wvFd-z7jSaA',
      'youtubeUrl': 'https://www.youtube.com/watch?v=wvFd-z7jSaA',
    },
    {
      'lec': 4,
      'title': 'Typography in Figma',
      'time': 14,
      'description': 'Learn how to work with text and create beautiful typography',
      'videoId': 'B242nuM3y2s',
      'youtubeUrl': 'https://www.youtube.com/watch?v=B242nuM3y2s',
    },
    {
      'lec': 5,
      'title': 'Colors and Gradients in Figma',
      'time': 16,
      'description': 'Explore color theory and gradient techniques in Figma',
      'videoId': 'HZuk6Wkx_Eg',
      'youtubeUrl': 'https://www.youtube.com/watch?v=HZuk6Wkx_Eg',
    },
    {
      'lec': 6,
      'title': 'Figma Components Tutorial',
      'time': 22,
      'description': 'Create reusable components and manage design systems',
      'videoId': 'k74IrUNaJVk',
      'youtubeUrl': 'https://www.youtube.com/watch?v=k74IrUNaJVk',
    },
    {
      'lec': 7,
      'title': 'Auto Layout in Figma',
      'time': 25,
      'description': 'Master responsive design with Figma\'s Auto Layout feature',
      'videoId': 'TyaGpGDFczw',
      'youtubeUrl': 'https://www.youtube.com/watch?v=TyaGpGDFczw',
    },
    {
      'lec': 8,
      'title': 'Figma Prototyping Tutorial',
      'time': 20,
      'description': 'Create interactive prototypes and animations',
      'videoId': 'X5qiBwqptek',
      'youtubeUrl': 'https://www.youtube.com/watch?v=X5qiBwqptek',
    },
    {
      'lec': 9,
      'title': 'Figma Collaboration Features',
      'time': 17,
      'description': 'Learn team collaboration and developer handoff best practices',
      'videoId': 'qAws7eXItMk',
      'youtubeUrl': 'https://www.youtube.com/watch?v=qAws7eXItMk',
    },
    {
      'lec': 10,
      'title': 'Advanced Figma Techniques',
      'time': 28,
      'description': 'Explore advanced Figma features and professional workflows',
      'videoId': 'Gu1so3pz4bA',
      'youtubeUrl': 'https://www.youtube.com/watch?v=Gu1so3pz4bA',
    },
    {
      'lec': 11,
      'title': 'Mobile App Design in Figma',
      'time': 35,
      'description': 'Complete mobile app design from concept to prototype',
      'videoId': 'PeGfX7W1mJk',
      'youtubeUrl': 'https://www.youtube.com/watch?v=PeGfX7W1mJk',
    },
    {
      'lec': 12,
      'title': 'Website Design in Figma',
      'time': 40,
      'description': 'Design a complete website using Figma best practices',
      'videoId': 'FK4YusHIIj0',
      'youtubeUrl': 'https://www.youtube.com/watch?v=FK4YusHIIj0',
    },
  ];

  /// Python Course with Real YouTube Videos
  static final List<Map<String, dynamic>> pythonLectures = [
    {
      'lec': 1,
      'title': 'Python Tutorial for Beginners',
      'time': 30,
      'description': 'Complete Python tutorial for absolute beginners',
      'videoId': '_uQrJ0TkZlc',
      'youtubeUrl': 'https://www.youtube.com/watch?v=_uQrJ0TkZlc',
    },
    {
      'lec': 2,
      'title': 'Python Variables and Data Types',
      'time': 25,
      'description': 'Learn about Python variables and different data types',
      'videoId': 'cQT33yu9pY8',
      'youtubeUrl': 'https://www.youtube.com/watch?v=cQT33yu9pY8',
    },
    {
      'lec': 3,
      'title': 'Python Functions',
      'time': 35,
      'description': 'Master Python functions and how to use them effectively',
      'videoId': 'BVfCWuca9nw',
      'youtubeUrl': 'https://www.youtube.com/watch?v=BVfCWuca9nw',
    },
    {
      'lec': 4,
      'title': 'Python Object-Oriented Programming',
      'time': 45,
      'description': 'Learn OOP concepts in Python with practical examples',
      'videoId': 'Ej_02ICOIgs',
      'youtubeUrl': 'https://www.youtube.com/watch?v=Ej_02ICOIgs',
    },
    {
      'lec': 5,
      'title': 'Python File Handling',
      'time': 20,
      'description': 'Work with files in Python - read, write, and manipulate',
      'videoId': 'ixEeeNjjOJ0',
      'youtubeUrl': 'https://www.youtube.com/watch?v=ixEeeNjjOJ0',
    },
  ];

  /// Web Development Course with Real YouTube Videos
  static final List<Map<String, dynamic>> webDevLectures = [
    {
      'lec': 1,
      'title': 'HTML Tutorial for Beginners',
      'time': 40,
      'description': 'Complete HTML tutorial from scratch',
      'videoId': 'UB1O30fR-EE',
      'youtubeUrl': 'https://www.youtube.com/watch?v=UB1O30fR-EE',
    },
    {
      'lec': 2,
      'title': 'CSS Tutorial for Beginners',
      'time': 50,
      'description': 'Learn CSS styling and layout techniques',
      'videoId': 'yfoY53QXEnI',
      'youtubeUrl': 'https://www.youtube.com/watch?v=yfoY53QXEnI',
    },
    {
      'lec': 3,
      'title': 'JavaScript Tutorial for Beginners',
      'time': 60,
      'description': 'Master JavaScript programming fundamentals',
      'videoId': 'PkZNo7MFNFg',
      'youtubeUrl': 'https://www.youtube.com/watch?v=PkZNo7MFNFg',
    },
    {
      'lec': 4,
      'title': 'React Tutorial for Beginners',
      'time': 75,
      'description': 'Build modern web applications with React',
      'videoId': 'Ke90Tje7VS0',
      'youtubeUrl': 'https://www.youtube.com/watch?v=Ke90Tje7VS0',
    },
  ];

  /// Get course lectures by course type
  static List<Map<String, dynamic>> getCourseLectures(String courseType) {
    switch (courseType.toLowerCase()) {
      case 'figma':
        return figmaLectures;
      case 'python':
        return pythonLectures;
      case 'webdev':
      case 'web development':
        return webDevLectures;
      default:
        return figmaLectures; // Default to Figma
    }
  }

  /// Get total course duration
  static int getTotalDuration(String courseType) {
    final lectures = getCourseLectures(courseType);
    return lectures.fold(0, (sum, lecture) => sum + (lecture['time'] as int));
  }

  /// Get formatted duration
  static String getFormattedDuration(String courseType) {
    final totalMinutes = getTotalDuration(courseType);
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  /// Get playlist URL for course
  static String? getPlaylistUrl(String courseType) {
    switch (courseType.toLowerCase()) {
      case 'figma':
        return figmaPlaylistUrl;
      default:
        return null;
    }
  }
}
