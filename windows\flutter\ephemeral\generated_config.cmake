# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter1\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\PROJECTS\\New folder\\aspirehub3" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter1\\flutter"
  "PROJECT_DIR=E:\\PROJECTS\\New folder\\aspirehub3"
  "FLUTTER_ROOT=C:\\flutter1\\flutter"
  "FLUTTER_EPHEMERAL_DIR=E:\\PROJECTS\\New folder\\aspirehub3\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\PROJECTS\\New folder\\aspirehub3"
  "FLUTTER_TARGET=E:\\PROJECTS\\New folder\\aspirehub3\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\PROJECTS\\New folder\\aspirehub3\\.dart_tool\\package_config.json"
)
