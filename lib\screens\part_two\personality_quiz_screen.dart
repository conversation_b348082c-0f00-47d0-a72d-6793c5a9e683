import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../Models/theme_provider.dart';
import '../../services/personality_service.dart';
import 'personality_result_screen.dart';

class PersonalityQuizScreen extends StatefulWidget {
  const PersonalityQuizScreen({super.key});

  @override
  State<PersonalityQuizScreen> createState() => _PersonalityQuizScreenState();
}

class _PersonalityQuizScreenState extends State<PersonalityQuizScreen> {
  int currentQuestionIndex = 0;
  List<Map<String, dynamic>> questions = [];
  List<Map<String, dynamic>> userAnswers = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _generateQuestions();
  }

  void _generateQuestions() {
    setState(() {
      questions = PersonalityService.generateQuizQuestions();
      userAnswers = List.filled(questions.length, {});
      isLoading = false;
    });
  }

  void _selectAnswer(Map<String, dynamic> answer) {
    setState(() {
      userAnswers[currentQuestionIndex] = answer;
    });
  }

  void _nextQuestion() {
    if (currentQuestionIndex < questions.length - 1) {
      setState(() {
        currentQuestionIndex++;
      });
    } else {
      _finishQuiz();
    }
  }

  void _previousQuestion() {
    if (currentQuestionIndex > 0) {
      setState(() {
        currentQuestionIndex--;
      });
    }
  }

  void _finishQuiz() {
    // Calculate personality type
    final personalityType = PersonalityService.calculatePersonalityType(userAnswers);
    
    // Save personality type
    PersonalityService.savePersonalityType(personalityType);
    
    // Navigate to result screen
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => PersonalityResultScreen(
          personalityType: personalityType,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    if (isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final currentQuestion = questions[currentQuestionIndex];
    final progress = (currentQuestionIndex + 1) / questions.length;
    final hasSelectedAnswer = userAnswers[currentQuestionIndex].isNotEmpty;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: Theme.of(context).iconTheme.color,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'اختبار الشخصية',
          style: TextStyle(
            color: Theme.of(context).textTheme.titleLarge?.color,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress Bar
            Container(
              width: double.infinity,
              height: 8.h,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progress,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        themeProvider.isDarkMode ? const Color(0xFF6A5ACD) : const Color(0xFF1C1259),
                        themeProvider.isDarkMode ? const Color(0xFF9370DB) : const Color(0xFF4169E1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Question Counter
            Text(
              'السؤال ${currentQuestionIndex + 1} من ${questions.length}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
            
            SizedBox(height: 24.h),
            
            // Question
            Text(
              currentQuestion['question'],
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.titleLarge?.color,
                height: 1.4,
              ),
            ),
            
            SizedBox(height: 32.h),
            
            // Answer Options
            Expanded(
              child: ListView.builder(
                itemCount: currentQuestion['answers'].length,
                itemBuilder: (context, index) {
                  final answer = currentQuestion['answers'][index];
                  final isSelected = userAnswers[currentQuestionIndex]['text'] == answer['text'];
                  
                  return Padding(
                    padding: EdgeInsets.only(bottom: 16.h),
                    child: GestureDetector(
                      onTap: () => _selectAnswer(answer),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(20.w),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (themeProvider.isDarkMode ? const Color(0xFF6A5ACD) : const Color(0xFF1C1259)).withOpacity(0.1)
                              : Theme.of(context).cardTheme.color,
                          border: Border.all(
                            color: isSelected
                                ? (themeProvider.isDarkMode ? const Color(0xFF6A5ACD) : const Color(0xFF1C1259))
                                : Theme.of(context).dividerColor.withOpacity(0.3),
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 20.w,
                              height: 20.h,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: isSelected
                                      ? (themeProvider.isDarkMode ? const Color(0xFF6A5ACD) : const Color(0xFF1C1259))
                                      : Theme.of(context).dividerColor,
                                  width: 2,
                                ),
                                color: isSelected
                                    ? (themeProvider.isDarkMode ? const Color(0xFF6A5ACD) : const Color(0xFF1C1259))
                                    : Colors.transparent,
                              ),
                              child: isSelected
                                  ? Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 12.sp,
                                    )
                                  : null,
                            ),
                            
                            SizedBox(width: 16.w),
                            
                            Expanded(
                              child: Text(
                                answer['text'],
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Theme.of(context).textTheme.bodyLarge?.color,
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Navigation Buttons
            Row(
              children: [
                if (currentQuestionIndex > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousQuestion,
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'السابق',
                        style: TextStyle(fontSize: 16.sp),
                      ),
                    ),
                  ),
                
                if (currentQuestionIndex > 0) SizedBox(width: 16.w),
                
                Expanded(
                  child: ElevatedButton(
                    onPressed: hasSelectedAnswer ? _nextQuestion : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeProvider.isDarkMode ? const Color(0xFF6A5ACD) : const Color(0xFF1C1259),
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      currentQuestionIndex == questions.length - 1 ? 'إنهاء' : 'التالي',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
