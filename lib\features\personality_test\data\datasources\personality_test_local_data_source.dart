import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/personality_result_model.dart';

/// Local data source for personality test
abstract class PersonalityTestLocalDataSource {
  Future<void> saveResult(PersonalityResultModel result);
  Future<PersonalityResultModel?> getSavedResult();
  Future<void> clearResult();
}

/// Implementation of personality test local data source
class PersonalityTestLocalDataSourceImpl implements PersonalityTestLocalDataSource {
  final SharedPreferences sharedPreferences;
  static const String cachedPersonalityResult = 'CACHED_PERSONALITY_RESULT';

  PersonalityTestLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> saveResult(PersonalityResultModel result) async {
    try {
      final jsonString = json.encode(result.toJson());
      await sharedPreferences.setString(cachedPersonalityResult, jsonString);
    } catch (e) {
      throw CacheException('Failed to save personality result: $e');
    }
  }

  @override
  Future<PersonalityResultModel?> getSavedResult() async {
    try {
      final jsonString = sharedPreferences.getString(cachedPersonalityResult);
      if (jsonString != null) {
        final jsonMap = json.decode(jsonString);
        return PersonalityResultModel.fromJson(jsonMap);
      }
      return null;
    } catch (e) {
      throw CacheException('Failed to get saved personality result: $e');
    }
  }

  @override
  Future<void> clearResult() async {
    try {
      await sharedPreferences.remove(cachedPersonalityResult);
    } catch (e) {
      throw CacheException('Failed to clear personality result: $e');
    }
  }
}
