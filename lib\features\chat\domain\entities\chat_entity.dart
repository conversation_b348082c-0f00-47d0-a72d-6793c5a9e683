import 'package:equatable/equatable.dart';

/// Domain entity for chat
class ChatEntity extends Equatable {
  final String id;
  final String userId;
  final String userName;
  final String userAvatar;
  final String lastMessage;
  final DateTime lastMessageTime;
  final bool isOnline;
  final MessageType lastMessageType;
  final bool isRead;

  const ChatEntity({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.isOnline,
    required this.lastMessageType,
    required this.isRead,
  });

  @override
  List<Object> get props => [
        id,
        userId,
        userName,
        userAvatar,
        lastMessage,
        lastMessageTime,
        isOnline,
        lastMessageType,
        isRead,
      ];
}

/// Message types
enum MessageType {
  text,
  image,
  voice,
}
