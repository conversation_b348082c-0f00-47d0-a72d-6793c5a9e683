import 'package:equatable/equatable.dart';

/// Domain entity for user
class UserEntity extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? profileImagePath;
  final UserType userType;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  const UserEntity({
    required this.id,
    required this.name,
    required this.email,
    this.profileImagePath,
    required this.userType,
    required this.createdAt,
    this.lastLoginAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        profileImagePath,
        userType,
        createdAt,
        lastLoginAt,
      ];
}

/// User types
enum UserType {
  student,
  company,
}
