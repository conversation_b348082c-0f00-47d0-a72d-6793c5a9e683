import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../Models/theme_provider.dart';
import '../../services/personality_service.dart';
import '../../models/career_tracks.dart';
import '../../widgets/personality_dashboard/personality_header.dart';
import '../../widgets/personality_dashboard/track_card.dart';
import '../../widgets/personality_dashboard/course_card.dart';
import 'play_list_screen.dart';

class PersonalityDashboard extends StatefulWidget {
  const PersonalityDashboard({super.key});

  @override
  State<PersonalityDashboard> createState() => _PersonalityDashboardState();
}

class _PersonalityDashboardState extends State<PersonalityDashboard> {
  String? userPersonality;
  Map<String, dynamic>? dashboardConfig;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPersonalityData();
  }

  Future<void> _loadPersonalityData() async {
    try {
      final personality = await PersonalityService.getPersonalityType();
      if (personality != null) {
        final config = PersonalityService.getDashboardConfig(personality);
        setState(() {
          userPersonality = personality;
          dashboardConfig = config;
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    if (isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (userPersonality == null || dashboardConfig == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.psychology_outlined, size: 80.sp, color: Colors.grey),
              SizedBox(height: 16.h),
              Text(
                'لم يتم تحديد شخصيتك بعد',
                style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8.h),
              Text(
                'قم بإجراء اختبار الشخصية أولاً',
                style: TextStyle(fontSize: 14.sp, color: Colors.grey),
              ),
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: () {
                  // Navigate to personality quiz
                },
                child: const Text('ابدأ اختبار الشخصية'),
              ),
            ],
          ),
        ),
      );
    }

    final personalityInfo = dashboardConfig!['personalityInfo'];
    final primaryTracks = List<String>.from(dashboardConfig!['primaryTracks']);
    final secondaryTracks = List<String>.from(
      dashboardConfig!['secondaryTracks'],
    );
    final recommendedCourses = List<Map<String, dynamic>>.from(
      dashboardConfig!['recommendedCourses'],
    );

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 40.h),

            // Personality Header
            PersonalityHeader(
              personalityType: userPersonality!,
              personalityName: personalityInfo['name'],
              description: personalityInfo['description'],
              color: Color(dashboardConfig!['personalityColor']),
            ),

            SizedBox(height: 30.h),

            // Primary Tracks Section
            if (primaryTracks.isNotEmpty) ...[
              Text(
                'المسارات المناسبة لك',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
              ),
              SizedBox(height: 16.h),

              SizedBox(
                height: 120.h,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: primaryTracks.length,
                  itemBuilder: (context, index) {
                    final trackName = primaryTracks[index];
                    final trackData = CareerTracks.getTrackData(trackName);

                    return Padding(
                      padding: EdgeInsets.only(right: 16.w),
                      child: TrackCard(
                        trackName: trackName,
                        trackData: trackData!,
                        isPrimary: true,
                        onTap: () => _navigateToTrackCourses(trackName),
                      ),
                    );
                  },
                ),
              ),

              SizedBox(height: 30.h),
            ],

            // Recommended Courses Section
            Text(
              'الكورسات المقترحة',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            SizedBox(height: 16.h),

            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount:
                  recommendedCourses.take(4).length, // Show first 4 courses
              itemBuilder: (context, index) {
                final course = recommendedCourses[index];

                return Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: CourseCard(
                    course: course,
                    onTap: () => _navigateToCourse(course),
                  ),
                );
              },
            ),

            SizedBox(height: 20.h),

            // Secondary Tracks Section
            if (secondaryTracks.isNotEmpty) ...[
              Text(
                'مسارات أخرى قد تهمك',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).textTheme.titleMedium?.color,
                ),
              ),
              SizedBox(height: 16.h),

              SizedBox(
                height: 100.h,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: secondaryTracks.length,
                  itemBuilder: (context, index) {
                    final trackName = secondaryTracks[index];
                    final trackData = CareerTracks.getTrackData(trackName);

                    return Padding(
                      padding: EdgeInsets.only(right: 16.w),
                      child: TrackCard(
                        trackName: trackName,
                        trackData: trackData!,
                        isPrimary: false,
                        onTap: () => _navigateToTrackCourses(trackName),
                      ),
                    );
                  },
                ),
              ),
            ],

            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }

  void _navigateToTrackCourses(String trackName) {
    // Navigate to track courses screen
    // This will be implemented based on your navigation structure
  }

  void _navigateToCourse(Map<String, dynamic> course) {
    // For now, navigate to Figma course if it's the Figma course
    if (course['name'] == 'Figma للمبتدئين') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const PlayListScreen()),
      );
    }
  }
}
