import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/personality_question_model.dart';

/// Remote data source for personality test
abstract class PersonalityTestRemoteDataSource {
  Future<List<PersonalityQuestionModel>> getQuestions();
}

/// Implementation of personality test remote data source
class PersonalityTestRemoteDataSourceImpl implements PersonalityTestRemoteDataSource {
  final http.Client client;

  PersonalityTestRemoteDataSourceImpl({required this.client});

  @override
  Future<List<PersonalityQuestionModel>> getQuestions() async {
    try {
      // For now, return mock data since we don't have a real API
      // In a real app, this would make an HTTP request
      return _getMockQuestions();
    } catch (e) {
      throw ServerException('Failed to fetch personality test questions: $e');
    }
  }

  List<PersonalityQuestionModel> _getMockQuestions() {
    return [
      const PersonalityQuestionModel(
        id: 1,
        question: 'How do you deal with stress and tension in your daily life?',
        options: [
          PersonalityOptionModel(
            text: 'I exercise or meditate.',
            trait: 'I',
            value: 2,
          ),
          PersonalityOptionModel(
            text: 'I share my feelings with friends.',
            trait: 'E',
            value: 2,
          ),
          PersonalityOptionModel(
            text: 'I face it alone and try to overcome it.',
            trait: 'I',
            value: 1,
          ),
        ],
        imagePath: AppConstants.testImagePath,
        category: 'Extraversion vs. Introversion',
      ),
      // Add more questions here...
    ];
  }
}
