import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/chat_entity.dart';
import '../entities/chat_message_entity.dart';

/// Repository interface for chat
abstract class ChatRepository {
  /// Get all chats
  Future<Either<Failure, List<ChatEntity>>> getChats();
  
  /// Get messages for a specific chat
  Future<Either<Failure, List<ChatMessageEntity>>> getMessages(String chatId);
  
  /// Send a message
  Future<Either<Failure, ChatMessageEntity>> sendMessage(ChatMessageEntity message);
  
  /// Create or get existing chat
  Future<Either<Failure, ChatEntity>> getOrCreateChat(
    String userId,
    String userName,
    String userAvatar,
  );
  
  /// Mark message as read
  Future<Either<Failure, void>> markMessageAsRead(String messageId);
  
  /// Delete message
  Future<Either<Failure, void>> deleteMessage(String messageId);
}
