import '../../domain/entities/personality_question_entity.dart';

/// Data model for personality question
class PersonalityQuestionModel extends PersonalityQuestionEntity {
  const PersonalityQuestionModel({
    required super.id,
    required super.question,
    required super.options,
    super.imagePath,
    required super.category,
  });

  factory PersonalityQuestionModel.fromJson(Map<String, dynamic> json) {
    return PersonalityQuestionModel(
      id: json['id'],
      question: json['question'],
      options: (json['options'] as List)
          .map((option) => PersonalityOptionModel.fromJson(option))
          .toList(),
      imagePath: json['imagePath'],
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options.map((option) => (option as PersonalityOptionModel).toJson()).toList(),
      'imagePath': imagePath,
      'category': category,
    };
  }

  factory PersonalityQuestionModel.fromEntity(PersonalityQuestionEntity entity) {
    return PersonalityQuestionModel(
      id: entity.id,
      question: entity.question,
      options: entity.options.map((option) => PersonalityOptionModel.fromEntity(option)).toList(),
      imagePath: entity.imagePath,
      category: entity.category,
    );
  }
}

/// Data model for personality option
class PersonalityOptionModel extends PersonalityOptionEntity {
  const PersonalityOptionModel({
    required super.text,
    required super.trait,
    required super.value,
  });

  factory PersonalityOptionModel.fromJson(Map<String, dynamic> json) {
    return PersonalityOptionModel(
      text: json['text'],
      trait: json['trait'],
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'trait': trait,
      'value': value,
    };
  }

  factory PersonalityOptionModel.fromEntity(PersonalityOptionEntity entity) {
    return PersonalityOptionModel(
      text: entity.text,
      trait: entity.trait,
      value: entity.value,
    );
  }
}
