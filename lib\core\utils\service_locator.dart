import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// Features
import '../../features/personality_test/data/datasources/personality_test_local_data_source.dart';
import '../../features/personality_test/data/datasources/personality_test_remote_data_source.dart';
import '../../features/personality_test/data/repositories/personality_test_repository_impl.dart';
import '../../features/personality_test/domain/repositories/personality_test_repository.dart';
import '../../features/personality_test/domain/usecases/get_personality_questions.dart';
import '../../features/personality_test/domain/usecases/calculate_personality_result.dart';

// Core
import '../network/network_info.dart';

final sl = GetIt.instance;

/// Initialize all dependencies
Future<void> init() async {
  //! Features - Personality Test
  _initPersonalityTest();

  //! Core
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());

  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => http.Client());
}

void _initPersonalityTest() {
  // Use cases
  sl.registerLazySingleton(() => GetPersonalityQuestions(sl()));
  sl.registerLazySingleton(() => CalculatePersonalityResult(sl()));

  // Repository
  sl.registerLazySingleton<PersonalityTestRepository>(
    () => PersonalityTestRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<PersonalityTestRemoteDataSource>(
    () => PersonalityTestRemoteDataSourceImpl(client: sl()),
  );

  sl.registerLazySingleton<PersonalityTestLocalDataSource>(
    () => PersonalityTestLocalDataSourceImpl(sharedPreferences: sl()),
  );
}
