import 'package:aspirehub3/screens/part_two/video_screen.dart';
import 'package:aspirehub3/widgets/videocard.dart';
import 'package:aspirehub3/widgets/course_header.dart';
import 'package:aspirehub3/services/youtube_player_service.dart';
import 'package:aspirehub3/models/youtube_courses.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PlayListScreen extends StatefulWidget {
  const PlayListScreen({super.key});

  @override
  State<PlayListScreen> createState() => _PlaylistscreenState();
}

class _PlaylistscreenState extends State<PlayListScreen> {
  // Get course data from YouTube courses
  List<Map<String, dynamic>> get videoData =>
      YouTubeCourses.getCourseLectures('figma');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: Theme.of(context).iconTheme.color,
            size: 20.sp,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Stack(
        children: [
          // Course header
          CourseHeader(
            courseTitle: 'Figma Course',
            duration: YouTubeCourses.getFormattedDuration('figma'),
            lessonsCount:
                YouTubeCourses.getCourseLectures('figma').length.toString(),
          ),
          Padding(
            padding: EdgeInsets.only(top: 210.h, left: 10.w),
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children: [
                  // Play entire playlist button
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 10.w,
                      vertical: 10.h,
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          await YouTubePlayerService.showPlaylistDialog(
                            context,
                            YouTubeCourses.figmaPlaylistId,
                            'Figma Course Playlist',
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE91E63),
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        icon: const Icon(
                          Icons.playlist_play,
                          color: Colors.white,
                        ),
                        label: Text(
                          'تشغيل البلاي ليست كاملة في YouTube',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Individual videos
                  ...videoData.map<Widget>((video) {
                    final index = videoData.indexOf(video);
                    return VideoCard(
                      lec: video['lec'],
                      time: video['time'],
                      index: index + 1,
                      targetPage: VideoPlayerScreen(
                        lectureData: video,
                        lectureIndex: index,
                        allLectures: videoData,
                      ),
                      title: video['title'],
                      description: video['description'],
                      videoId: video['videoId'],
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
