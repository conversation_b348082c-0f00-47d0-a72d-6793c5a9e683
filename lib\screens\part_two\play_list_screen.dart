import 'package:aspirehub3/Models/theme_provider.dart';
import 'package:aspirehub3/screens/part_two/video_screen.dart';
import 'package:aspirehub3/widgets/circularprogress.dart';
import 'package:aspirehub3/widgets/videocard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class PlayListScreen extends StatefulWidget {
  const PlayListScreen({super.key});

  @override
  State<PlayListScreen> createState() => _PlaylistscreenState();
}

class _PlaylistscreenState extends State<PlayListScreen> {
  // Figma Course Playlist from YouTube
  // Playlist URL: https://youtube.com/playlist?list=PLjzhiGLyugKwnM6uN4NXhfpU8L7XvtDEv&si=G7k_2PB99a5JItta
  List<Map<String, dynamic>> videoData = [
    {
      'lec': 1,
      'title': 'Figma Tutorial for Beginners - Introduction',
      'time': 15,
      'description': 'Learn the basics of Figma interface and tools',
      'videoId': 'FTFaQWZBqQ8', // Example video ID
    },
    {
      'lec': 2,
      'title': 'Setting Up Your First Project',
      'time': 12,
      'description':
          'Create your first Figma project and understand the workspace',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 3,
      'title': 'Working with Frames and Shapes',
      'time': 18,
      'description': 'Master the fundamental building blocks of Figma design',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 4,
      'title': 'Typography and Text Styling',
      'time': 14,
      'description':
          'Learn how to work with text and create beautiful typography',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 5,
      'title': 'Colors and Gradients',
      'time': 16,
      'description': 'Explore color theory and gradient techniques in Figma',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 6,
      'title': 'Components and Variants',
      'time': 22,
      'description': 'Create reusable components and manage design systems',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 7,
      'title': 'Auto Layout Mastery',
      'time': 25,
      'description':
          'Master responsive design with Figma\'s Auto Layout feature',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 8,
      'title': 'Prototyping and Interactions',
      'time': 20,
      'description': 'Create interactive prototypes and animations',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 9,
      'title': 'Collaboration and Handoff',
      'time': 17,
      'description':
          'Learn team collaboration and developer handoff best practices',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 10,
      'title': 'Advanced Design Techniques',
      'time': 28,
      'description':
          'Explore advanced Figma features and professional workflows',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 11,
      'title': 'Mobile App Design Project',
      'time': 35,
      'description': 'Complete mobile app design from concept to prototype',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 12,
      'title': 'Web Design Project',
      'time': 40,
      'description': 'Design a complete website using Figma best practices',
      'videoId': 'FTFaQWZBqQ8',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: Theme.of(context).iconTheme.color,
            size: 20.sp,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Stack(
        children: [
          Padding(
            padding: EdgeInsets.all(18.w),
            child: SizedBox(
              width: double.infinity,
              height: 155.h,
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Theme.of(context).colorScheme.surface.withAlpha(50)
                          : const Color(0xFFE5F2FA),
                  borderRadius: BorderRadius.all(Radius.circular(10.r)),
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 20.h, left: 20.w),
            child: Row(
              children: [
                Expanded(
                  child: ListTile(
                    title: Text(
                      'Figma Course ',
                      style: TextStyle(
                        color: Theme.of(context).textTheme.titleLarge?.color,
                        fontWeight: FontWeight.w800,
                        fontSize: 20.sp,
                      ),
                    ),
                    subtitle: Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time_filled_sharp,
                            color: Theme.of(context).iconTheme.color,
                            size: 18.sp,
                          ),
                          Text(
                            ' 4.5 hours • 12 lessons',
                            style: TextStyle(
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                              fontWeight: FontWeight.w500,
                              fontSize: 14.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const CircularProgress(),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 210.h, left: 10.w),
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children:
                    videoData.map((video) {
                      final index = videoData.indexOf(video);
                      return VideoCard(
                        lec: video['lec'],
                        time: video['time'],
                        index: index + 1,
                        targetPage: VideoPlayerScreen(
                          lectureData: video,
                          lectureIndex: index,
                          allLectures: videoData,
                        ),
                        title: video['title'],
                        description: video['description'],
                        videoId: video['videoId'],
                      );
                    }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
