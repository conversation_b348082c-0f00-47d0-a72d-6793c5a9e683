import 'package:aspirehub3/screens/part_two/video_screen.dart';
import 'package:aspirehub3/widgets/videocard.dart';
import 'package:aspirehub3/widgets/course_header.dart';
import 'package:aspirehub3/services/course_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PlayListScreen extends StatefulWidget {
  const PlayListScreen({super.key});

  @override
  State<PlayListScreen> createState() => _PlaylistscreenState();
}

class _PlaylistscreenState extends State<PlayListScreen> {
  // Get course data from service
  List<Map<String, dynamic>> get videoData =>
      CourseService.getCourseData('figma');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: Theme.of(context).iconTheme.color,
            size: 20.sp,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Stack(
        children: [
          // Course header
          CourseHeader(
            courseTitle: 'Figma Course',
            duration: CourseService.getCourseDuration('figma'),
            lessonsCount: CourseService.getLessonsCount('figma').toString(),
          ),
          Padding(
            padding: EdgeInsets.only(top: 210.h, left: 10.w),
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                children:
                    videoData.map<Widget>((video) {
                      final index = videoData.indexOf(video);
                      return VideoCard(
                        lec: video['lec'],
                        time: video['time'],
                        index: index + 1,
                        targetPage: VideoPlayerScreen(
                          lectureData: video,
                          lectureIndex: index,
                          allLectures: videoData,
                        ),
                        title: video['title'],
                        description: video['description'],
                        videoId: video['videoId'],
                      );
                    }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
