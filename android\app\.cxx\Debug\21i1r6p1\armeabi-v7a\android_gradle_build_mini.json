{"buildFiles": ["C:\\flutter1\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PROJECTS\\New folder\\aspirehub3\\android\\app\\.cxx\\Debug\\21i1r6p1\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\PROJECTS\\New folder\\aspirehub3\\android\\app\\.cxx\\Debug\\21i1r6p1\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}