/// 16 Personality Types System
class PersonalityTypes {
  
  /// All 16 personality types with their characteristics
  static const Map<String, Map<String, dynamic>> personalityData = {
    'INTJ': {
      'name': 'المهندس المعماري',
      'description': 'مفكر استراتيجي مع خطة لكل شيء',
      'traits': ['تحليلي', 'مستقل', 'حازم', 'مبدع'],
      'strengths': ['التفكير الاستراتيجي', 'الاستقلالية', 'حل المشاكل المعقدة'],
      'careers': ['مهندس برمجيات', 'محلل أنظمة', 'مهندس معماري', 'عالم بيانات'],
      'tracks': ['تكنولوجيا', 'هندسة', 'علوم البيانات'],
      'color': 0xFF6A5ACD,
    },
    'INTP': {
      'name': 'المفكر',
      'description': 'مخترع مبدع مع عطش لا ينضب للمعرفة',
      'traits': ['فضولي', 'مرن', 'منطقي', 'مبدع'],
      'strengths': ['التحليل المنطقي', 'الإبداع', 'حل المشاكل'],
      'careers': ['باحث', 'عالم', 'مطور برمجيات', 'محلل'],
      'tracks': ['بحث وتطوير', 'تكنولوجيا', 'علوم'],
      'color': 0xFF4169E1,
    },
    'ENTJ': {
      'name': 'القائد',
      'description': 'قائد جريء وقوي الإرادة',
      'traits': ['قيادي', 'حازم', 'استراتيجي', 'منظم'],
      'strengths': ['القيادة', 'التخطيط الاستراتيجي', 'اتخاذ القرارات'],
      'careers': ['مدير تنفيذي', 'رائد أعمال', 'مستشار إداري', 'محامي'],
      'tracks': ['إدارة أعمال', 'ريادة أعمال', 'قانون'],
      'color': 0xFF8B0000,
    },
    'ENTP': {
      'name': 'المناقش',
      'description': 'مفكر ذكي وفضولي لا يستطيع مقاومة التحدي الفكري',
      'traits': ['مبدع', 'متحمس', 'مرن', 'مقنع'],
      'strengths': ['الإبداع', 'التواصل', 'حل المشاكل'],
      'careers': ['مسوق', 'صحفي', 'مستشار', 'مطور منتجات'],
      'tracks': ['تسويق', 'إعلام', 'استشارات'],
      'color': 0xFF32CD32,
    },
    'INFJ': {
      'name': 'المحامي',
      'description': 'مثالي هادئ ومتصوف ولكنه ملهم وحازم',
      'traits': ['حدسي', 'مثالي', 'منظم', 'مبدع'],
      'strengths': ['التعاطف', 'الرؤية المستقبلية', 'الإلهام'],
      'careers': ['مستشار نفسي', 'كاتب', 'مدرس', 'مصمم'],
      'tracks': ['علم النفس', 'تعليم', 'تصميم', 'كتابة'],
      'color': 0xFF9370DB,
    },
    'INFP': {
      'name': 'الوسيط',
      'description': 'شاعر مثالي ولطيف ومتحمس دائماً لقضية جيدة',
      'traits': ['مثالي', 'مرن', 'فضولي', 'مبدع'],
      'strengths': ['الإبداع', 'التعاطف', 'المرونة'],
      'careers': ['فنان', 'كاتب', 'مصمم جرافيك', 'معالج نفسي'],
      'tracks': ['فنون', 'تصميم', 'كتابة', 'علم النفس'],
      'color': 0xFF20B2AA,
    },
    'ENFJ': {
      'name': 'البطل',
      'description': 'قائد ملهم وحريص ومتحمس',
      'traits': ['قيادي', 'متعاطف', 'منظم', 'ملهم'],
      'strengths': ['القيادة', 'التواصل', 'التحفيز'],
      'careers': ['مدرس', 'مدرب', 'مستشار', 'مدير موارد بشرية'],
      'tracks': ['تعليم', 'تدريب', 'موارد بشرية'],
      'color': 0xFF228B22,
    },
    'ENFP': {
      'name': 'الناشط',
      'description': 'روح حرة متحمسة ومبدعة واجتماعية',
      'traits': ['متحمس', 'مبدع', 'اجتماعي', 'مرن'],
      'strengths': ['الإبداع', 'التواصل', 'التحفيز'],
      'careers': ['مسوق', 'صحفي', 'مدرب', 'مصمم'],
      'tracks': ['تسويق', 'إعلام', 'تصميم'],
      'color': 0xFFFF6347,
    },
    'ISTJ': {
      'name': 'اللوجستي',
      'description': 'حقيقي وعملي ومسؤول',
      'traits': ['منظم', 'مسؤول', 'عملي', 'موثوق'],
      'strengths': ['التنظيم', 'الموثوقية', 'الدقة'],
      'careers': ['محاسب', 'مدير مشروع', 'محلل مالي', 'مراجع'],
      'tracks': ['محاسبة', 'إدارة مشاريع', 'مالية'],
      'color': 0xFF4682B4,
    },
    'ISFJ': {
      'name': 'المدافع',
      'description': 'حامي دافئ ومخلص ودائماً مستعد للدفاع عن أحبائه',
      'traits': ['مخلص', 'متعاطف', 'مسؤول', 'صبور'],
      'strengths': ['الدعم', 'التعاطف', 'الموثوقية'],
      'careers': ['ممرض', 'مدرس', 'أخصائي اجتماعي', 'مساعد إداري'],
      'tracks': ['رعاية صحية', 'تعليم', 'خدمة اجتماعية'],
      'color': 0xFF87CEEB,
    },
    'ESTJ': {
      'name': 'المدير التنفيذي',
      'description': 'منظم ممتاز لإدارة الأشياء أو الأشخاص',
      'traits': ['منظم', 'قيادي', 'عملي', 'حازم'],
      'strengths': ['الإدارة', 'التنظيم', 'القيادة'],
      'careers': ['مدير', 'محامي', 'قاضي', 'ضابط'],
      'tracks': ['إدارة', 'قانون', 'أمن'],
      'color': 0xFF8B4513,
    },
    'ESFJ': {
      'name': 'القنصل',
      'description': 'شخص اجتماعي شديد الاهتمام ومستعد دائماً للمساعدة',
      'traits': ['اجتماعي', 'متعاطف', 'منظم', 'مساعد'],
      'strengths': ['التواصل', 'التعاون', 'الدعم'],
      'careers': ['مدرس', 'مستشار', 'منسق فعاليات', 'مدير علاقات عامة'],
      'tracks': ['تعليم', 'علاقات عامة', 'تنظيم فعاليات'],
      'color': 0xFFDDA0DD,
    },
    'ISTP': {
      'name': 'الحرفي',
      'description': 'مجرب جريء وعملي وسيد جميع أنواع الأدوات',
      'traits': ['عملي', 'مرن', 'مستقل', 'منطقي'],
      'strengths': ['حل المشاكل العملية', 'المرونة', 'التحليل'],
      'careers': ['مهندس ميكانيكي', 'فني', 'طيار', 'رياضي'],
      'tracks': ['هندسة', 'تقنية', 'رياضة'],
      'color': 0xFF696969,
    },
    'ISFP': {
      'name': 'المغامر',
      'description': 'فنان مرن وساحر ودائماً مستعد لاستكشاف إمكانيات جديدة',
      'traits': ['مبدع', 'مرن', 'حساس', 'مستقل'],
      'strengths': ['الإبداع', 'التكيف', 'التعاطف'],
      'careers': ['فنان', 'مصور', 'مصمم', 'موسيقي'],
      'tracks': ['فنون', 'تصميم', 'موسيقى'],
      'color': 0xFFFF69B4,
    },
    'ESTP': {
      'name': 'رجل الأعمال',
      'description': 'ذكي ونشيط ومدرك جداً لمحيطه',
      'traits': ['نشيط', 'عملي', 'اجتماعي', 'مرن'],
      'strengths': ['التكيف', 'التواصل', 'حل المشاكل السريع'],
      'careers': ['مندوب مبيعات', 'رياضي', 'رجل أعمال', 'مفاوض'],
      'tracks': ['مبيعات', 'رياضة', 'أعمال'],
      'color': 0xFFFF4500,
    },
    'ESFP': {
      'name': 'المؤدي',
      'description': 'شخص عفوي ونشيط ومتحمس',
      'traits': ['متحمس', 'اجتماعي', 'مرن', 'مبدع'],
      'strengths': ['التواصل', 'الإبداع', 'التحفيز'],
      'careers': ['ممثل', 'مدرس', 'مصمم', 'منسق فعاليات'],
      'tracks': ['فنون أدائية', 'تعليم', 'تصميم'],
      'color': 0xFFFFD700,
    },
  };

  /// Get personality data by type
  static Map<String, dynamic>? getPersonalityData(String type) {
    return personalityData[type.toUpperCase()];
  }

  /// Get all personality types
  static List<String> getAllTypes() {
    return personalityData.keys.toList();
  }

  /// Get tracks for personality type
  static List<String> getTracksForPersonality(String type) {
    final data = getPersonalityData(type);
    return data != null ? List<String>.from(data['tracks']) : [];
  }

  /// Get careers for personality type
  static List<String> getCareersForPersonality(String type) {
    final data = getPersonalityData(type);
    return data != null ? List<String>.from(data['careers']) : [];
  }

  /// Get color for personality type
  static int getColorForPersonality(String type) {
    final data = getPersonalityData(type);
    return data != null ? data['color'] : 0xFF6A5ACD;
  }
}
