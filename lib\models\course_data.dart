/// Course data models and constants
class CourseData {
  static const String figmaPlaylistUrl =
      'https://youtube.com/playlist?list=PLjzhiGLyugKwnM6uN4NXhfpU8L7XvtDEv&si=G7k_2PB99a5JItta';

  /// All courses organized by category
  static final Map<String, List<Map<String, dynamic>>> coursesByCategory = {
    'تصميم': [
      {
        'id': 'figma_course',
        'name': 'Figma للمبتدئين',
        'description': 'تعلم أساسيات التصميم باستخدام Figma',
        'duration': '4.5 ساعة',
        'level': 'مبتدئ',
        'lectures': figmaLectures,
        'image': 'figma_course.png',
        'color': 0xFFE91E63,
      },
    ],
    'تكنولوجيا': [
      {
        'id': 'python_course',
        'name': 'برمجة Python',
        'description': 'تعلم أساسيات البرمجة بلغة Python',
        'duration': '40 ساعة',
        'level': 'مبتدئ',
        'lectures': [], // Will be added later
        'image': 'python_course.png',
        'color': 0xFF1E88E5,
      },
    ],
    'إدارة أعمال': [
      {
        'id': 'management_course',
        'name': 'أساسيات الإدارة',
        'description': 'مبادئ الإدارة والقيادة',
        'duration': '30 ساعة',
        'level': 'مبتدئ',
        'lectures': [], // Will be added later
        'image': 'management_course.png',
        'color': 0xFF4CAF50,
      },
    ],
  };

  /// Figma Course Lectures Data
  static final List<Map<String, dynamic>> figmaLectures = [
    {
      'lec': 1,
      'title': 'Figma Tutorial for Beginners - Introduction',
      'time': 15,
      'description': 'Learn the basics of Figma interface and tools',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 2,
      'title': 'Setting Up Your First Project',
      'time': 12,
      'description':
          'Create your first Figma project and understand the workspace',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 3,
      'title': 'Working with Frames and Shapes',
      'time': 18,
      'description': 'Master the fundamental building blocks of Figma design',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 4,
      'title': 'Typography and Text Styling',
      'time': 14,
      'description':
          'Learn how to work with text and create beautiful typography',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 5,
      'title': 'Colors and Gradients',
      'time': 16,
      'description': 'Explore color theory and gradient techniques in Figma',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 6,
      'title': 'Components and Variants',
      'time': 22,
      'description': 'Create reusable components and manage design systems',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 7,
      'title': 'Auto Layout Mastery',
      'time': 25,
      'description':
          'Master responsive design with Figma\'s Auto Layout feature',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 8,
      'title': 'Prototyping and Interactions',
      'time': 20,
      'description': 'Create interactive prototypes and animations',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 9,
      'title': 'Collaboration and Handoff',
      'time': 17,
      'description':
          'Learn team collaboration and developer handoff best practices',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 10,
      'title': 'Advanced Design Techniques',
      'time': 28,
      'description':
          'Explore advanced Figma features and professional workflows',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 11,
      'title': 'Mobile App Design Project',
      'time': 35,
      'description': 'Complete mobile app design from concept to prototype',
      'videoId': 'FTFaQWZBqQ8',
    },
    {
      'lec': 12,
      'title': 'Web Design Project',
      'time': 40,
      'description': 'Design a complete website using Figma best practices',
      'videoId': 'FTFaQWZBqQ8',
    },
  ];

  /// Get total course duration in minutes
  static int get figmaTotalDuration {
    return figmaLectures.fold(
      0,
      (sum, lecture) => sum + (lecture['time'] as int),
    );
  }

  /// Get course duration in hours and minutes format
  static String get figmaFormattedDuration {
    final totalMinutes = figmaTotalDuration;
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    return '${hours}h ${minutes}m';
  }
}
