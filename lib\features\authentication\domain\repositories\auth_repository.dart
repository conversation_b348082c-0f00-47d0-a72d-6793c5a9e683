import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user_entity.dart';

/// Repository interface for authentication
abstract class AuthRepository {
  /// Sign in with email and password
  Future<Either<Failure, UserEntity>> signInWithEmailAndPassword(
    String email,
    String password,
  );
  
  /// Sign up with email and password
  Future<Either<Failure, UserEntity>> signUpWithEmailAndPassword(
    String name,
    String email,
    String password,
    UserType userType,
  );
  
  /// Sign out
  Future<Either<Failure, void>> signOut();
  
  /// Get current user
  Future<Either<Failure, UserEntity?>> getCurrentUser();
  
  /// Reset password
  Future<Either<Failure, void>> resetPassword(String email);
  
  /// Update user profile
  Future<Either<Failure, UserEntity>> updateUserProfile(UserEntity user);
  
  /// Check if user is signed in
  Future<Either<Failure, bool>> isSignedIn();
}
